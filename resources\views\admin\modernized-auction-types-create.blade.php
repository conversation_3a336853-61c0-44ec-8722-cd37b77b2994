@extends('layouts.modernized-admin')

@section('title', 'Create Auction Category - Vertigo AMS')

@section('page-title', 'Create Auction Category')
@section('page-subtitle', 'Add a new auction category to organize your auctions.')

@section('quick-actions')
<div class="flex space-x-2">
    <a href="{{ route('auction-types.index') }}" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        <span class="hidden lg:inline">Back to Categories</span>
        <span class="lg:hidden">Back</span>
    </a>
    <a href="{{ route('v1.auction-types.create') }}" class="flex items-center bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-all duration-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        <span class="hidden lg:inline">Original Form</span>
        <span class="lg:hidden">Original</span>
    </a>
</div>
@endsection

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Form Card -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-900">Category Details</h3>
            <p class="text-sm text-gray-600 mt-1">Fill in the information below to create a new auction category</p>
        </div>
        
        <form id="auction-type-form" method="POST" action="{{ route('auction-types.store') }}" enctype="multipart/form-data" class="p-6" novalidate>
            @csrf
            
            <!-- Category Name -->
            <div class="mb-6">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                    Category Name <span class="text-red-500">*</span>
                </label>
                <input type="text" 
                       id="name" 
                       name="name" 
                       value="{{ old('name') }}" 
                       required
                       maxlength="255"
                       placeholder="Enter category name"
                       class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm @error('name') border-red-300 @enderror">
                @error('name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Category Type -->
            <div class="mb-6">
                <label for="type" class="block text-sm font-medium text-gray-700 mb-2">
                    Category Type <span class="text-red-500">*</span>
                </label>
                <select id="type" 
                        name="type" 
                        required
                        class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm @error('type') border-red-300 @enderror">
                    <option value="">Select category type...</option>
                    <option value="online" {{ old('type') == 'online' ? 'selected' : '' }}>Online</option>
                    <option value="live" {{ old('type') == 'live' ? 'selected' : '' }}>Live</option>
                    <option value="cash" {{ old('type') == 'cash' ? 'selected' : '' }}>Cash Sale</option>
                </select>
                @error('type')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-1 text-sm text-gray-500">Choose the type of auction this category will be used for</p>
            </div>

            <!-- Description -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-2">
                    <label for="description" class="block text-sm font-medium text-gray-700">
                        Description
                    </label>
                    <span id="description-count" class="text-sm text-gray-400">0/255</span>
                </div>
                <textarea id="description"
                          name="description"
                          rows="4"
                          maxlength="255"
                          placeholder="Enter a description for this category (optional)"
                          class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm @error('description') border-red-300 @enderror">{{ old('description') }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-1 text-sm text-gray-500">Provide additional details about this category</p>
            </div>

            <!-- Select Products -->
            <div class="mb-6">
                <label for="items" class="block text-sm font-medium text-gray-700 mb-2">
                    Select Products
                </label>
                <select id="items"
                        name="items[]"
                        multiple
                        class="w-full border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500 @error('items') border-red-300 @enderror"
                        placeholder="Search and select products...">
                    @php $selectedItems = old('items', []) @endphp
                    @foreach($items as $item)
                    <option value="{{ $item->id }}"
                            {{ in_array($item->id, $selectedItems) ? 'selected' : '' }}
                            data-image="{{ $item->getFirstMediaUrl('images') ?: asset('assets/img/160x160/img1.jpg') }}">
                        {{ $item->name }}
                    </option>
                    @endforeach
                </select>
                @error('items')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                <p class="mt-1 text-sm text-gray-500">Search and select products to include in this category</p>
            </div>

            <!-- Image Upload Section -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-2">
                    <label class="block text-sm font-medium text-gray-700">
                        Category Images
                    </label>
                    <span id="image-count" class="text-sm text-gray-400">0 images selected</span>
                </div>
                <div id="upload-area" class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors duration-200 cursor-pointer">
                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <div class="mt-4">
                        <label for="media" class="cursor-pointer">
                            <span class="mt-2 block text-sm font-medium text-gray-900">
                                Click to upload or drag and drop
                            </span>
                            <span class="mt-1 block text-sm text-gray-500">
                                PNG, JPG, GIF up to 10MB each
                            </span>
                        </label>
                        <input id="media" name="media[]" type="file" multiple accept="image/*" class="sr-only">
                    </div>
                </div>

                <!-- Image Preview Area -->
                <div id="image-preview" class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 hidden"></div>

                @error('media')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
                @error('media.*')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{{ route('auction-types.index') }}"
                   class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    Cancel
                </a>
                <button type="submit" 
                        class="px-6 py-2 text-sm font-medium text-white bg-gradient-to-r from-primary-500 to-primary-600 border border-transparent rounded-lg hover:from-primary-600 hover:to-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 shadow-lg hover:shadow-xl">
                    <svg class="h-4 w-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Create Category
                </button>
            </div>
        </form>
    </div>

    <!-- Help Section -->
    <div class="mt-8 bg-blue-50 rounded-xl p-6 border border-blue-200">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Category Types Explained</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li><strong>Online:</strong> Digital auctions where bidders participate remotely</li>
                        <li><strong>Live:</strong> In-person auctions with real-time bidding</li>
                        <li><strong>Cash Sale:</strong> Direct sales without bidding process</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const form = document.getElementById('auction-type-form');
    const nameInput = document.getElementById('name');
    const typeSelect = document.getElementById('type');
    const descriptionTextarea = document.getElementById('description');
    const descriptionCount = document.getElementById('description-count');
    const itemsSelect = document.getElementById('items');
    const fileInput = document.getElementById('media');
    const uploadArea = document.getElementById('upload-area');
    const imagePreview = document.getElementById('image-preview');
    const imageCount = document.getElementById('image-count');

    let tomSelect = null;

    // Initialize TomSelect for items dropdown
    if (itemsSelect) {
        tomSelect = new TomSelect(itemsSelect, {
            plugins: ['remove_button'],
            placeholder: 'Search and select products...',
            maxItems: null,
            valueField: 'value',
            labelField: 'text',
            searchField: 'text',
            render: {
                option: function(data, escape) {
                    const imageUrl = data.image || '{{ asset('assets/img/160x160/img1.jpg') }}';
                    return `
                        <div class="flex items-center py-2">
                            <img src="${escape(imageUrl)}" alt="${escape(data.text)}" class="w-8 h-8 rounded-full mr-3 object-cover">
                            <span>${escape(data.text)}</span>
                        </div>
                    `;
                },
                item: function(data, escape) {
                    return `<div class="flex items-center"><span>${escape(data.text)}</span></div>`;
                }
            },
            load: function(query, callback) {
                // Convert existing options to the format TomSelect expects
                const options = Array.from(itemsSelect.querySelectorAll('option')).map(option => ({
                    value: option.value,
                    text: option.textContent.trim(),
                    image: option.dataset.image
                })).filter(option => option.value); // Remove empty options

                if (!query.length) {
                    callback(options);
                    return;
                }

                // Filter options based on query
                const filtered = options.filter(option =>
                    option.text.toLowerCase().includes(query.toLowerCase())
                );
                callback(filtered);
            }
        });
    }

    // Description character counter
    if (descriptionTextarea && descriptionCount) {
        descriptionTextarea.addEventListener('input', function() {
            const count = this.value.length;
            descriptionCount.textContent = `${count}/255`;

            if (count > 200) {
                descriptionCount.classList.add('text-red-500');
                descriptionCount.classList.remove('text-gray-400');
            } else {
                descriptionCount.classList.remove('text-red-500');
                descriptionCount.classList.add('text-gray-400');
            }

            // Clear validation errors on input
            clearFieldError(this);
        });

        // Initial count
        descriptionCount.textContent = `${descriptionTextarea.value.length}/255`;
    }

    // Real-time validation for name field
    if (nameInput) {
        nameInput.addEventListener('input', function() {
            clearFieldError(this);
        });
    }

    // Real-time validation for type field
    if (typeSelect) {
        typeSelect.addEventListener('change', function() {
            clearFieldError(this);
        });
    }

    // File upload handling
    if (fileInput && uploadArea && imagePreview && imageCount) {
        // Click handler for upload area
        uploadArea.addEventListener('click', function(e) {
            if (e.target !== fileInput) {
                fileInput.click();
            }
        });

        // Drag and drop handlers
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('border-primary-300', 'bg-primary-50');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('border-primary-300', 'bg-primary-50');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('border-primary-300', 'bg-primary-50');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelection(files);
            }
        });

        // File input change handler
        fileInput.addEventListener('change', function(e) {
            handleFileSelection(e.target.files);
        });
    }

    function handleFileSelection(files) {
        if (files.length === 0) return;

        // Update image count
        imageCount.textContent = `${files.length} image${files.length !== 1 ? 's' : ''} selected`;

        // Clear previous previews
        imagePreview.innerHTML = '';
        imagePreview.classList.remove('hidden');

        // Create previews
        Array.from(files).forEach((file, index) => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const previewDiv = document.createElement('div');
                    previewDiv.className = 'relative group';
                    previewDiv.innerHTML = `
                        <img src="${e.target.result}" alt="Preview ${index + 1}" class="w-full h-24 object-cover rounded-lg border border-gray-200">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center">
                            <span class="text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200">${file.name}</span>
                        </div>
                    `;
                    imagePreview.appendChild(previewDiv);
                };
                reader.readAsDataURL(file);
            }
        });

        // Update upload area appearance
        uploadArea.classList.add('border-primary-300', 'bg-primary-50');
        uploadArea.classList.remove('border-gray-300');
    }

    // Form validation
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Clear previous errors
            clearAllErrors();

            let isValid = true;

            // Validate name
            if (!nameInput.value.trim()) {
                showFieldError(nameInput, 'Category name is required.');
                isValid = false;
            } else if (nameInput.value.length > 255) {
                showFieldError(nameInput, 'Category name must not exceed 255 characters.');
                isValid = false;
            }

            // Validate type
            if (!typeSelect.value) {
                showFieldError(typeSelect, 'Please select a category type.');
                isValid = false;
            }

            // Validate description length
            if (descriptionTextarea.value.length > 255) {
                showFieldError(descriptionTextarea, 'Description must not exceed 255 characters.');
                isValid = false;
            }

            if (isValid) {
                // Show loading state
                const submitButton = form.querySelector('button[type="submit"]');
                const originalText = submitButton.innerHTML;
                submitButton.disabled = true;
                submitButton.innerHTML = `
                    <svg class="animate-spin h-4 w-4 mr-2 inline" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Creating...
                `;

                // Submit the form
                this.submit();
            }
        });
    }

    function clearFieldError(field) {
        field.classList.remove('border-red-300', 'focus:ring-red-500', 'focus:border-red-500');
        field.classList.add('border-gray-300', 'focus:ring-primary-500', 'focus:border-primary-500');

        const errorMsg = field.parentNode.querySelector('.text-red-600');
        if (errorMsg) {
            errorMsg.remove();
        }
    }

    function clearAllErrors() {
        const errorFields = document.querySelectorAll('.border-red-300');
        errorFields.forEach(field => {
            field.classList.remove('border-red-300', 'focus:ring-red-500', 'focus:border-red-500');
            field.classList.add('border-gray-300', 'focus:ring-primary-500', 'focus:border-primary-500');
        });

        const errorMessages = document.querySelectorAll('.text-red-600');
        errorMessages.forEach(msg => {
            if (msg.classList.contains('mt-1')) {
                msg.remove();
            }
        });
    }

    function showFieldError(field, message) {
        field.classList.add('border-red-300', 'focus:ring-red-500', 'focus:border-red-500');
        field.classList.remove('border-gray-300', 'focus:ring-primary-500', 'focus:border-primary-500');

        // Remove existing error message
        const existingError = field.parentNode.querySelector('.text-red-600');
        if (existingError) {
            existingError.remove();
        }

        // Add new error message
        const errorDiv = document.createElement('p');
        errorDiv.className = 'mt-1 text-sm text-red-600 flex items-center';
        errorDiv.innerHTML = `
            <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            ${message}
        `;
        field.parentNode.appendChild(errorDiv);
    }

    console.log('Enhanced Auction Category Create page loaded');
});
</script>
@endpush
