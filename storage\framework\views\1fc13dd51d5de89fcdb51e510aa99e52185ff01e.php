<?php $__env->startSection('title', 'Edit Item - Vertigo AMS'); ?>

<?php $__env->startSection('page-title', 'Edit Item'); ?>
<?php $__env->startSection('page-subtitle', 'Update item information and details'); ?>

<?php $__env->startSection('quick-actions'); ?>
<div class="flex space-x-2">
    <a href="<?php echo e(route('items.show', $item)); ?>" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
        </svg>
        <span class="hidden lg:inline">View Item</span>
        <span class="lg:hidden">View</span>
    </a>
    <a href="<?php echo e(route('items.index')); ?>" class="flex items-center bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
        </svg>
        <span class="hidden lg:inline">All Items</span>
        <span class="lg:hidden">Items</span>
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Item Status Banner -->
<div class="mb-8">
    <?php if($item->closed_by): ?>
    <div class="bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="h-12 w-12 bg-white/20 rounded-lg flex items-center justify-center mr-4">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-xl font-bold">Item Sold - Limited Editing</h3>
                    <p class="text-yellow-100">This item has been sold. Some fields may be restricted from editing.</p>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="h-12 w-12 bg-white/20 rounded-lg flex items-center justify-center mr-4">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-xl font-bold">Editing: <?php echo e($item->name); ?></h3>
                    <p class="text-blue-100">Update item information and auction details</p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-sm text-blue-100">Last Updated</p>
                <p class="font-semibold"><?php echo e($item->updated_at ? $item->updated_at->format('M d, Y') : 'Never'); ?></p>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Progress Steps -->
<div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 mb-8">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div class="flex items-center">
                <div class="h-8 w-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                <span class="ml-2 text-sm font-medium text-primary-600">Update Details</span>
            </div>
            <div class="h-px w-12 bg-gray-300"></div>
            <div class="flex items-center">
                <div class="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center text-gray-500 font-bold text-sm">2</div>
                <span class="ml-2 text-sm font-medium text-gray-500">Review Changes</span>
            </div>
            <div class="h-px w-12 bg-gray-300"></div>
            <div class="flex items-center">
                <div class="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center text-gray-500 font-bold text-sm">3</div>
                <span class="ml-2 text-sm font-medium text-gray-500">Save</span>
            </div>
        </div>
        <div class="text-sm text-gray-500">
            Step 1 of 3
        </div>
    </div>
</div>

<!-- Edit Form -->
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <div class="h-8 w-8 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mr-3">
                    <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                </div>
                Update Item Information
            </h3>
            <div class="flex items-center space-x-2">
                <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full font-medium">Editing</span>
                <span class="text-xs text-gray-500"><?php echo e(now()->format('M d, Y h:i A')); ?></span>
            </div>
        </div>
    </div>

    <div class="p-6">
        <!-- Form Instructions -->
        <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h4 class="text-sm font-medium text-orange-800">Editing Item: <?php echo e($item->name); ?></h4>
                    <div class="mt-1 text-sm text-orange-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Review and update item details as needed</li>
                            <li>Ensure all required fields are completed</li>
                            <li>Upload new images or manage existing ones</li>
                            <?php if($item->closed_by): ?>
                            <li><strong>Note:</strong> This item has been sold - some restrictions may apply</li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form -->
        <form method="POST" action="<?php echo e(route('items.update', $item)); ?>" enctype="multipart/form-data" class="space-y-6">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            <?php echo $__env->make('app.items.modernized-form-inputs', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            
            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <div class="flex items-center space-x-4">
                    <a href="<?php echo e(route('items.show', $item)); ?>" class="text-gray-600 hover:text-gray-900 transition-colors duration-200">
                        Cancel Changes
                    </a>
                    <?php if(!$item->closed_by): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $item)): ?>
                        <button type="button" onclick="confirmDelete()" class="text-red-600 hover:text-red-900 transition-colors duration-200">
                            Delete Item
                        </button>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                <div class="flex items-center space-x-3">
                    <button type="button" class="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg font-medium hover:bg-gray-200 transition-all duration-200">
                        Save as Draft
                    </button>
                    <button type="submit" class="bg-gradient-to-r from-orange-600 to-orange-700 text-white px-6 py-2 rounded-lg font-medium hover:from-orange-700 hover:to-orange-800 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <svg class="h-4 w-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Update Item
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Item History -->
<div class="mt-8 bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <div class="h-8 w-8 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center mr-3">
                <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            Item History
        </h3>
    </div>
    <div class="p-6">
        <div class="space-y-4">
            <div class="flex items-center justify-between py-3 border-b border-gray-100">
                <div class="flex items-center">
                    <div class="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                        <svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">Item Created</p>
                        <p class="text-xs text-gray-500"><?php echo e($item->created_at ? $item->created_at->format('M d, Y h:i A') : 'Unknown'); ?></p>
                    </div>
                </div>
                <span class="text-xs text-gray-500"><?php echo e(optional($item->createdBy)->name ?? 'System'); ?></span>
            </div>

            <?php if($item->updated_at && $item->updated_at != $item->created_at): ?>
            <div class="flex items-center justify-between py-3 border-b border-gray-100">
                <div class="flex items-center">
                    <div class="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">Last Updated</p>
                        <p class="text-xs text-gray-500"><?php echo e($item->updated_at->format('M d, Y h:i A')); ?></p>
                    </div>
                </div>
                <span class="text-xs text-gray-500"><?php echo e(optional($item->updatedBy)->name ?? 'System'); ?></span>
            </div>
            <?php endif; ?>

            <?php if($item->closed_by): ?>
            <div class="flex items-center justify-between py-3">
                <div class="flex items-center">
                    <div class="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                        <svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">Item Sold</p>
                        <p class="text-xs text-gray-500">Sale completed</p>
                    </div>
                </div>
                <span class="text-xs text-gray-500"><?php echo e(optional($item->closedBy)->name ?? 'System'); ?></span>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<?php if(!$item->closed_by): ?>
    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $item)): ?>
    <div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-md mx-4">
            <div class="flex items-center mb-4">
                <div class="h-10 w-10 bg-red-100 rounded-full flex items-center justify-center mr-4">
                    <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900">Delete Item</h3>
            </div>
            <p class="text-sm text-gray-500 mb-6">Are you sure you want to delete this item? This action cannot be undone.</p>
            <div class="flex justify-end space-x-3">
                <button type="button" onclick="closeDeleteModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200">
                    Cancel
                </button>
                <form action="<?php echo e(route('items.destroy', $item)); ?>" method="POST" class="inline">
                    <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition-colors duration-200">
                        Delete Item
                    </button>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function confirmDelete() {
    document.getElementById('deleteModal').classList.remove('hidden');
    document.getElementById('deleteModal').classList.add('flex');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
    document.getElementById('deleteModal').classList.remove('flex');
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Item edit form loaded');
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.modernized-admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/items/modernized-edit.blade.php ENDPATH**/ ?>