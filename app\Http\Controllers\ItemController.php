<?php

namespace App\Http\Controllers;

use App\Models\Item;
use App\Models\Status;
use App\Models\Auction;
use App\Models\AuctionType;
use Illuminate\Http\Request;
use App\Http\Requests\ItemStoreRequest;
use App\Http\Requests\ItemUpdateRequest;

use Facades\App\Cache\Repo;
use Facades\App\Libraries\AuctionHandler;
use Facades\App\Libraries\InvoiceHandler;

class ItemController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', Item::class);

        $items = Item::search($request->search)
                    ->latest();
        if(request()->type == 'sold') {
            $items->whereNotNull("closed_by");
        } else {
            $items->whereNull("closed_by");
        }
        $items = $items->get();

        return view('app.items.index', compact('items'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', Item::class);

        $auctionTypes = AuctionType::get();
        $statuses = Status::pluck("name", 'id');

        return view('app.items.create', compact('auctionTypes', 'statuses'));
    }

    /**
     * @param \App\Http\Requests\ItemStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(ItemStoreRequest $request)
    {
        $this->authorize('create', Item::class);

        $validated = $request->validated();
        
        // $validated['user_id'] = AuctionHandler::mapUserId();

        $item = Item::find(request()->item_id);
        
        if( $item ) {
            $item->update($validated);
        } else {
            $item = Item::create($validated);
        }

        if( request()->media ) {
            foreach (request()->media as $file) {
                $item = AuctionHandler::uploadImageJob($item, $file, 'media');
            }
        }

        return redirect()
            ->back()
            // ->route('items.edit', $item)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Item $item
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, Item $item)
    {
        $this->authorize('view', $item);

        return view('app.items.show', compact('item'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Item $item
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Item $item)
    {
        $this->authorize('update', $item);

        $auctionTypes = AuctionType::get();
        $statuses = Status::pluck("name", 'id');

        return view(
            'app.items.edit',
            compact('item', 'auctionTypes', 'statuses')
        );
    }

    /**
     * @param \App\Http\Requests\ItemUpdateRequest $request
     * @param \App\Models\Item $item
     * @return \Illuminate\Http\Response
     */
    public function update(ItemUpdateRequest $request, Item $item)
    {
        $this->authorize('update', $item);

        $validated = $request->validated();
        
        // $validated['user_id'] = AuctionHandler::mapUserId();

        $item->update($validated);

        if( request()->media ) {
            foreach (request()->media as $file) {
                $item = AuctionHandler::uploadImageJob($item, $file, 'media');
            }
        }

        return redirect()
            ->back()
            // ->route('items.edit', $item)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Item $item
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, Item $item)
    {
        $this->authorize('delete', $item);

        $item->delete();

        return redirect()
            ->route('items.index')
            ->withSuccess(__('crud.common.removed'));
    }

    public function ajaxItems(Request $request) {
        $items = Repo::getItems();
        return response($items);
    }

    public function ajaxItem(Request $request, Item $item) {
        // Load the auctionType relationship to ensure proper type detection
        $item->load('auctionType');
        return response($item);
    }

    /**
     * Get all bids for a specific item (public endpoint)
     */
    public function bids(Request $request, Item $item) {
        $bids = $item->auctions()
            ->with(['user', 'auctionType'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'data' => $bids,
            'item' => $item
        ]);
    }

    public function viewItem( Request $request, Item $item){

        return view("frontend.item", compact("item"));
    }

    public function viewCashItem( Request $request, Item $item){

        return view("frontend.view-cash-item", compact("item"));
    }

    public function addToCart( Request $request, Item $item ) {
        $type = $item->auctionType->type ?? '';
        if( ! $type ) return redirect()->back()->withError('Invalid item type');

        // Get current cart items and quantities
        $items = session($type) ?? [];
        $quantities = session($type . '_quantities') ?? [];
        $quantity = $request->input('quantity', 1);

        // Validate using CartValidator
        $validation = \App\Validators\CartValidator::validateAddToCart($item, $quantity, $items);
        if (!$validation['valid']) {
            return redirect()->back()->withError($validation['message']);
        }

        // Add item to cart if not already present
        if( ! in_array( $item->id , $items ) ) {
            $items[] = $item->id;
            $quantities[$item->id] = $quantity;
        } else {
            // Update quantity if item already in cart
            $quantities[$item->id] = ($quantities[$item->id] ?? 1) + $quantity;
        }

        // Save to session
        session([$type => $items]);
        session([$type . '_quantities' => $quantities]);

        if( request()->checkout ) return redirect('/checkout');
        return redirect()->back()->withSuccess( $item->name . " Added to Cart (Qty: {$quantities[$item->id]})" );
    }

    public function removeFromCart(Request $request, Item $item ) {
        $type = $item->auctionType->type ?? '';
        if( ! $type ) return redirect()->back();

        // Get current cart items and quantities
        $items = session($type) ?? [];
        $quantities = session($type . '_quantities') ?? [];
        $quantityToRemove = $request->input('quantity', null);

        // If no specific quantity provided, remove entire item
        if ($quantityToRemove === null) {
            $items = array_diff( $items , [ $item->id ] );
            unset($quantities[$item->id]);
        } else {
            // Reduce quantity
            $currentQuantity = $quantities[$item->id] ?? 1;
            $newQuantity = $currentQuantity - $quantityToRemove;

            if ($newQuantity <= 0) {
                // Remove item completely if quantity becomes 0 or negative
                $items = array_diff( $items , [ $item->id ] );
                unset($quantities[$item->id]);
            } else {
                $quantities[$item->id] = $newQuantity;
            }
        }

        // Save to session
        session([$type => $items]);
        session([$type . '_quantities' => $quantities]);

        return redirect()->back()->withSuccess( $item->name . " Removed from Cart" );
    }

    public function cart(Request $request) {
        $items = Repo::cart();
        return view("frontend.cart", compact("items"));
    }

    public function checkout(Request $request) {
        $items = Repo::cart();
        return view("frontend.checkout", compact("items"));
    }

    public function checkoutSave(Request $request) {
        $order = InvoiceHandler::saveAnOrder();
        session()->forget('cash');
        return redirect('/')->withSuccess("Thank you for ordering with us.");
    }

    // Modernized Admin Interface Methods

    /**
     * Display modernized items index page
     */
    public function modernizedIndex(Request $request)
    {
        $this->authorize('view-any', Item::class);

        $items = Item::search($request->search)
                    ->when($request->type == 'sold', function($query) {
                        return $query->whereNotNull("closed_by");
                    }, function($query) {
                        return $query->whereNull("closed_by");
                    })
                    ->latest()
                    ->paginate(10);

        return view('app.items.modernized-index', compact('items'));
    }

    /**
     * Show modernized create form
     */
    public function modernizedCreate(Request $request)
    {
        $this->authorize('create', Item::class);

        $statuses = \App\Models\Status::whereIn("id", [20,21,22])->pluck("name", "id");
        $users = \App\Models\User::whereHas("roles", function($q){ $q->where('name', 'supplier');})->pluck("name", "id");
        $auctionTypes = \App\Models\AuctionType::all();

        return view('app.items.modernized-create', compact('statuses', 'users', 'auctionTypes'));
    }

    /**
     * Display modernized item details
     */
    public function modernizedShow(Request $request, Item $item)
    {
        $this->authorize('view', $item);

        return view('app.items.modernized-show', compact('item'));
    }

    /**
     * Show modernized edit form
     */
    public function modernizedEdit(Request $request, Item $item)
    {
        $this->authorize('update', $item);

        $statuses = \App\Models\Status::whereIn("id", [20,21,22])->pluck("name", "id");
        $users = \App\Models\User::whereHas("roles", function($q){ $q->where('name', 'supplier');})->pluck("name", "id");
        $auctionTypes = \App\Models\AuctionType::all();

        return view('app.items.modernized-edit', compact('item', 'statuses', 'users', 'auctionTypes'));
    }

    /**
     * Store a newly created item via modernized interface.
     */
    public function modernizedStore(ItemStoreRequest $request)
    {
        $this->authorize('create', Item::class);

        $validated = $request->validated();

        $item = Item::find(request()->item_id);

        if( $item ) {
            $item->update($validated);
            $message = 'Item updated successfully!';
        } else {
            $item = Item::create($validated);
            $message = 'Item created successfully!';
        }

        if( request()->media ) {
            foreach (request()->media as $file) {
                $item = AuctionHandler::uploadImageJob($item, $file, 'media');
            }
        }

        return redirect()
            ->route('items.show', $item)
            ->withSuccess($message);
    }

    /**
     * Update the item via modernized interface.
     */
    public function modernizedUpdate(Request $request, Item $item)
    {
        $this->authorize('update', $item);

        // Use the existing update method
        return $this->update($request, $item);
    }

    /**
     * Delete the item via modernized interface.
     */
    public function modernizedDestroy(Request $request, Item $item)
    {
        $this->authorize('delete', $item);

        // Use the existing destroy method
        return $this->destroy($request, $item);
    }
}
