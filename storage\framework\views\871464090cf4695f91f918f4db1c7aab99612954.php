

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Supplier Details</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Supplier::class)): ?>
                <a href="<?php echo e(route('suppliers.index')); ?>" class="btn btn-primary me-2">
                    <i class="bi bi-arrow-left me-1"></i>
                    Back
                </a>
                <?php endif; ?>

                <?php if(!$supplier->closed_by): ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $supplier)): ?>
                <a href="<?php echo e(route('suppliers.edit', $supplier)); ?>" class="btn btn-primary">
                    <i class="bi bi-pencil-square me-1"></i>
                    Edit
                </a>
                <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title">Supplier Information</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-4">
                        <div class="flex-shrink-0">
                            <img class="avatar avatar-xl" src="<?php echo e($supplier->image ?? '/assets/img/160x160/img1.jpg'); ?>" alt="Image Description">
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo e($supplier->name ?? '-'); ?></h3>
                            <p class="card-text"><?php echo e($supplier->phone ?? '-'); ?></p>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6>Email</h6>
                        <p><?php echo e($supplier->email ?? '-'); ?></p>
                    </div>

                    <div class="mb-3">
                        <h6>Address</h6>
                        <p><?php echo e($supplier->address ?? '-'); ?></p>
                    </div>

                    <div class="mb-3">
                        <h6>Branch</h6>
                        <p><?php echo e($supplier->branch->name ?? '-'); ?></p>
                    </div>

                    <div class="mb-3">
                        <h6>Total Items</h6>
                        <p><?php echo e(_number($supplier->items->count()) ?? '0'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title">Supplier Items</h5>
                        <a href="?item_id=" class="btn btn-primary btn-sm">
                            <i class="bi bi-plus-circle me-1"></i>
                            Add Item
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table js-datatable table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                            <thead class="thead-light">
                                <tr>
                                    <th>#</th>
                                    <th class="text-left">
                                        <?php echo app('translator')->get('crud.items.inputs.name'); ?>
                                    </th>
                                    <th class="text-left">
                                        <?php echo app('translator')->get('crud.items.inputs.auction_type_id'); ?>
                                    </th>
                                    <th class="text-left">
                                        Price
                                    </th>
                                    <th class="text-left">
                                        Date
                                    </th>
                                    <th class="text-center">
                                        <?php echo app('translator')->get('crud.common.actions'); ?>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $supplier->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $i): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr class="<?php if($i->closed_by): ?> alert alert-soft-warning btn-light <?php endif; ?> " >
                                    <td><?php echo e($key + 1); ?></td>
                                    <td class="table-column-ps-0">
                                      <a class="d-flex align-items-center" href="/items/<?php echo e($i->id ?? '-'); ?>">
                                        <div class="flex-shrink-0">
                                            <img class="avatar avatar-lg" src="<?php echo e($i->image ?? '-'); ?>" alt="Image Description">
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h5 class="text-inherit mb-0">
                                                <?php echo e($i->name ?? ''); ?>

                                            </h5>
                                            <small class="text-info">
                                                <?php echo e($i->reference_number ?? ''); ?>

                                            </small>
                                        </div>
                                      </a>
                                    </td>
                                    <td>
                                        <div><?php echo e(optional($i->auctionType)->name ?? '-'); ?></div>
                                        <small class="text-info"><?php echo e($i->auctionType->type ?? '-'); ?></small>
                                    </td>
                                    <td>
                                        <?php echo e(_money( $i->target_amount )); ?>

                                    </td>
                                    <td>
                                        <small>
                                            <?php echo e($i->date_from ?? '-'); ?>

                                            <br> To <br>
                                            <?php echo e($i->date_to ?? '-'); ?>

                                        </small>
                                    </td>

                                    <td class="text-center" style="width: 134px;">
                                        <div
                                            role="group"
                                            aria-label="Row Actions"
                                            class="btn-group"
                                        >
                                            <?php if(!$i->closed_by): ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $i)): ?>
                                            <a href="?item_id=<?php echo e($i->id); ?>">
                                                <button type="button" class="btn btn-primary btn-sm m-1">
                                                    <i class="bi bi-pencil-square me-1"></i>
                                                    Edit
                                                </button>
                                            </a>
                                            <?php endif; ?>
                                            <?php endif; ?>

                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $i)): ?>
                                            <a href="<?php echo e(route('items.show', $i)); ?>">
                                                <button type="button" class="btn btn-info btn-sm m-1 text-white">
                                                    <i class="bi bi-eye me-1"></i>
                                                    View
                                                </button>
                                            </a>
                                            <?php endif; ?>

                                            <?php if(!$i->closed_by): ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $i)): ?>
                                            <form
                                                action="<?php echo e(route('items.destroy', $i)); ?>"
                                                method="POST"
                                                onsubmit="return confirm('<?php echo e(__('crud.common.are_you_sure')); ?>')"
                                            >
                                                <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-danger btn-sm m-1">
                                                    <i class="bi bi-trash me-1"></i>
                                                    Delete
                                                </button>
                                            </form>
                                            <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="6" class="text-center">No items found</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <?php if(request()->has('item_id')): ?>
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title">
                        <?php if(request()->item_id): ?>
                        Edit Item
                        <?php else: ?>
                        Add New Item
                        <?php endif; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form','data' => ['method' => 'POST','action' => ''.e(route('items.store')).'','hasFiles' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['method' => 'POST','action' => ''.e(route('items.store')).'','has-files' => true]); ?>
                        <?php echo $__env->make('app.items.form-inputs', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <input type="hidden" name="item_id" value="<?php echo e($item->id ?? ''); ?>">

                        <div class="mt-4 text-end">
                            <a href="<?php echo e(route('suppliers.show', $supplier)); ?>" class="btn btn-light me-2">
                                <i class="bi bi-x-circle me-1"></i>
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save me-1"></i>
                                Save Item
                            </button>
                        </div>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <?php if(count($supplier->getMedia('media')) > 0): ?>
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-title">Supplier Images</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <?php $__currentLoopData = $supplier->getMedia('media'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-sm-2 col-md-1 mb-3 position-relative">
                    <a href="/delete-media-get/<?php echo e($file->id); ?>" class="btn btn-sm btn-danger position-absolute top-0 end-0"
                       style="z-index: 1;" title="Delete">
                        <i class="bi bi-x"></i>
                    </a>
                    <a href="<?php echo e($file->getUrl('image')); ?>" data-fslightbox="gallery" class="d-block">
                        <img class="img-fluid rounded" src="<?php echo e($file->getUrl('cropped')); ?>" alt="Image">
                    </a>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    // =======================================================
    dataTableBtn(".js-datatable", null, [0, 'asc'])
  });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/suppliers/show.blade.php ENDPATH**/ ?>