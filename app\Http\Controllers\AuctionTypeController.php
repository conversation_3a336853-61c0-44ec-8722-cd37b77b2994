<?php

namespace App\Http\Controllers;

use App\Models\Item;
use App\Models\User;
use App\Models\Status;
use App\Models\AuctionType;
use Illuminate\Http\Request;
use App\Http\Requests\AuctionTypeStoreRequest;
use App\Http\Requests\AuctionTypeUpdateRequest;
use Facades\App\Cache\Repo;

class AuctionTypeController extends Controller
{

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', AuctionType::class);

        $auctionTypes = AuctionType::search($request->search)
            ->whereIn("type", ["online", "cash"])
            ->latest()
            ->get();

        return view(
            'app.auction_types.index',
            compact('auctionTypes')
        );
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', AuctionType::class);

        $statuses = Status::pluck('name', 'id');
        $users = User::pluck('name', 'id');
        $items = Item::whereNull('closed_by')->whereNull('auction_type_id')->get();

        return view(
            'app.auction_types.create',
            compact('statuses', 'users', 'users', 'items')
        );
    }

    /**
     * @param \App\Http\Requests\AuctionTypeStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(AuctionTypeStoreRequest $request)
    {
        $this->authorize('create', AuctionType::class);

        $validated = $request->validated();

        $auctionType = AuctionType::create($validated);

        $auctionType->items()->update(['auction_type_id' => null]);

        Item::whereIn("id", (request()->items ?? []) )->update([
            "auction_type_id" => $auctionType->id,
        ]);

        if( request()->media ) {
            foreach (request()->media as $file) {
                $auctionType->addMedia($file)->toMediaCollection("media");
            }
        }

        return redirect()
            ->route('auction-types.edit', $auctionType)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\AuctionType $auctionType
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, AuctionType $auctionType)
    {
        $this->authorize('view', $auctionType);

        return view('app.auction_types.show', compact('auctionType'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\AuctionType $auctionType
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, AuctionType $auctionType)
    {
        $this->authorize('update', $auctionType);

        $statuses = Status::pluck('name', 'id');
        $users = User::pluck('name', 'id');
        $items = Item::whereNull('closed_by')
                    ->where( function($query) use ($auctionType) {
                        $query->where('auction_type_id', $auctionType->id);
                        $query->orWhereNull('auction_type_id');
                    })
                    ->get();

        return view(
            'app.auction_types.edit',
            compact('auctionType', 'statuses', 'users', 'users', 'items')
        );
    }

    /**
     * @param \App\Http\Requests\AuctionTypeUpdateRequest $request
     * @param \App\Models\AuctionType $auctionType
     * @return \Illuminate\Http\Response
     */
    public function update(
        AuctionTypeUpdateRequest $request,
        AuctionType $auctionType
    ) {
        $this->authorize('update', $auctionType);

        $validated = $request->validated();

        $auctionType->update($validated);

        if( request()->media ) {
            foreach (request()->media as $file) {
                $auctionType->addMedia($file)->toMediaCollection("media");
            }
        }

        $auctionType->items()->update(['auction_type_id' => null]);

        Item::whereIn("id", (request()->items ?? []) )->update([
            "auction_type_id" => $auctionType->id,
        ]);

        return redirect()
            ->route('auction-types.edit', $auctionType)
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\AuctionType $auctionType
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, AuctionType $auctionType)
    {
        $this->authorize('delete', $auctionType);

        $auctionType->delete();

        return redirect()
            ->route('auction-types.index')
            ->withSuccess(__('crud.common.removed'));
    }

    public function ajaxAuctionTypes(Request $request) {
        $auctionTypes = \Facades\App\Cache\Repo::getAuctionTypes();
        return response($auctionTypes);
    }

    /**
     * Modernized index view for auction types
     */
    public function modernizedIndex(Request $request)
    {
        $this->authorize('view-any', AuctionType::class);

        $auctionTypes = AuctionType::search($request->search)
            ->whereIn("type", ["online", "cash"])
            ->latest()
            ->get();

        return view(
            'admin.modernized-auction-types-index',
            compact('auctionTypes')
        );
    }

    /**
     * Modernized create view for auction types
     */
    public function modernizedCreate(Request $request)
    {
        $this->authorize('create', AuctionType::class);

        $statuses = Status::pluck('name', 'id');
        $users = User::pluck('name', 'id');
        $items = Item::whereNull('closed_by')->whereNull('auction_type_id')->get();

        return view(
            'admin.modernized-auction-types-create',
            compact('statuses', 'users', 'items')
        );
    }

    /**
     * Modernized show view for auction types
     */
    public function modernizedShow(Request $request, AuctionType $auctionType)
    {
        $this->authorize('view', $auctionType);

        return view('admin.modernized-auction-types-show', compact('auctionType'));
    }

    /**
     * Modernized edit view for auction types
     */
    public function modernizedEdit(Request $request, AuctionType $auctionType)
    {
        $this->authorize('update', $auctionType);

        $statuses = Status::pluck('name', 'id');
        $users = User::pluck('name', 'id');
        $items = Item::whereNull('closed_by')
                    ->where( function($query) use ($auctionType) {
                        $query->where('auction_type_id', $auctionType->id);
                        $query->orWhereNull('auction_type_id');
                    })
                    ->get();

        return view(
            'admin.modernized-auction-types-edit',
            compact('auctionType', 'statuses', 'users', 'items')
        );
    }

    /**
     * Store a newly created auction type via modernized interface.
     */
    public function modernizedStore(AuctionTypeStoreRequest $request)
    {
        $this->authorize('create', AuctionType::class);

        // Use the existing store method with proper request type
        return $this->store($request);
    }

    /**
     * Update the auction type via modernized interface.
     */
    public function modernizedUpdate(Request $request, AuctionType $auctionType)
    {
        $this->authorize('update', $auctionType);

        // Use the existing update method
        return $this->update($request, $auctionType);
    }

    /**
     * Delete the auction type via modernized interface.
     */
    public function modernizedDestroy(Request $request, AuctionType $auctionType)
    {
        $this->authorize('delete', $auctionType);

        // Use the existing destroy method
        return $this->destroy($request, $auctionType);
    }

}
