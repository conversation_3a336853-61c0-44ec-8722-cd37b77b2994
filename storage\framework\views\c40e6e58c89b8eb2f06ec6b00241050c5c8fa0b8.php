

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Edit Supplier</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\User::class)): ?>
                <a href="<?php echo e(route('suppliers.index')); ?>" class="btn btn-primary">
                    <i class="bi bi-arrow-left me-1"></i>
                    Back
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title">Supplier Details</h5>
        </div>
        <div class="card-body">
            <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form','data' => ['method' => 'PUT','action' => ''.e(route('suppliers.update', $supplier)).'','hasFiles' => true,'class' => 'mt-2']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['method' => 'PUT','action' => ''.e(route('suppliers.update', $supplier)).'','has-files' => true,'class' => 'mt-2']); ?>
                <?php echo $__env->make('app.suppliers.form-inputs', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                <div class="mt-4 text-end">
                    <a href="<?php echo e(route('suppliers.index')); ?>" class="btn btn-light me-2">
                        <i class="bi bi-arrow-left me-1"></i>
                        <?php echo app('translator')->get('crud.common.back'); ?>
                    </a>

                    <a href="<?php echo e(route('suppliers.create')); ?>" class="btn btn-light me-2">
                        <i class="bi bi-plus-circle me-1"></i>
                        <?php echo app('translator')->get('crud.common.create'); ?>
                    </a>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save me-1"></i>
                        <?php echo app('translator')->get('crud.common.update'); ?>
                    </button>
                </div>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-title">Supplier Images</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <?php $__currentLoopData = $supplier->getMedia('media'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-sm-2 col-md-1 mb-3 position-relative">
                    <a href="/delete-media-get/<?php echo e($file->id); ?>" class="btn btn-sm btn-danger position-absolute top-0 end-0"
                       style="z-index: 1;" title="Delete">
                        <i class="bi bi-x"></i>
                    </a>
                    <a href="<?php echo e($file->getUrl('image')); ?>" data-fslightbox="gallery" class="d-block">
                        <img class="img-fluid rounded" src="<?php echo e($file->getUrl('cropped')); ?>" alt="Image">
                    </a>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/suppliers/edit.blade.php ENDPATH**/ ?>